package org.biosino.lf.mash.mashweb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件路径配置类
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
@Data
@Component
@ConfigurationProperties(prefix = "file")
public class FileProperties {

    /**
     * 基础数据目录
     */
    private String baseDataDir;


    private String taskDir;

    /**
     * 脚本目录
     */
    private String scriptDir;
    

    /**
     * 获取任务工作目录
     */
    public String getTaskWorkDir(String taskId) {
        return taskDir + "/" + taskId;
    }

    /**
     * 获取任务输入目录
     */
    public String getTaskInputDir(String taskId) {
        return getTaskWorkDir(taskId) + "/input";
    }

    /**
     * 获取任务输出目录
     */
    public String getTaskOutputDir(String taskId) {
        return getTaskWorkDir(taskId) + "/output";
    }
}
