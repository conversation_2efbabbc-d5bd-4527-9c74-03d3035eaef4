<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style-end">

</th:block>
<div class="page-content" layout:fragment="content">
    <style>
        .tooltip-inner {
            background: #fafafa !important;
            text-align: left !important;
            color: #363636 !important;
            border: 1px solid #dedede;
            max-width: 400px !important;
        }

        .tooltip-arrow {
            border-bottom-color: #ffffff !important;
            opacity: 0;
        }

        .tooltip {
            opacity: 1 !important;
        }
    </style>

    <section class="p-top-50 p-bottom-50">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="modules__content">
                        <div class="withdraw_module withdraw_history bg-white">
                            <div class="d-flex justify-content-between p-2 border-bottom position-relative mb-2">
                                <div class="withdraw_table_header p-2 border-0">
                                    <h4 class="text-info">Sample Comparison</h4>
                                </div>
                            </div>

                            <div class="px-1 mb-2">
                                <div class="row">
                                    <input type="hidden" th:value="${taskId}" id="taskId">
                                    <div class="col-md-12">
                                        <div class="d-flex align-items-center justify-content-end px-2 mb-3">
                                            <ul class="nav results-tabs">
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  active domain-change-btn" name="domain"
                                                       value="A"
                                                       data-toggle="tab"
                                                    >Archaea</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  domain-change-btn" name="domain"
                                                       value="B"
                                                       data-toggle="tab"
                                                    >Bacteria</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  domain-change-btn" name="domain"
                                                       value="E"
                                                       data-toggle="tab"
                                                    >Eukaryota</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  domain-change-btn" name="domain"
                                                       value="V"
                                                       data-toggle="tab"
                                                    >Viruses</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="d-flex align-items-center justify-content-end px-2 mb-3">
                                            <ul class="nav results-tabs">
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  active level-change-btn"
                                                       data-toggle="tab"
                                                       name="level" value="P"
                                                    >Phylum</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  level-change-btn" data-toggle="tab"
                                                       name="level" value="C"
                                                    >Class</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  level-change-btn" data-toggle="tab"
                                                       name="level" value="O"
                                                    >Order</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  level-change-btn" data-toggle="tab"
                                                       name="level" value="F"
                                                    >Family</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  level-change-btn" data-toggle="tab"
                                                       name="level" value="G"
                                                    >Genus</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  level-change-btn" data-toggle="tab"
                                                       name="level" value="S"
                                                    >Species</a>
                                                </li>
                                            </ul>

                                        </div>
                                        <h5 class="text-center">Taxonomic Composition of Selected Samples<i
                                                class="fa fa-info-circle ml-1" data-toggle="tooltip"
                                                data-placement="right" data-container="body"
                                                data-html="true"
                                                title="The stacked bar plot shows the relative abundances of dominant groups at different taxonomic levels. The taxonomy and abundance were estimated based on the outputs from Kraken2 (Wood <i>et al</i>., 2019, Lu <i>et al</i>., 2022) and Bracken (Lu <i>et al</i>., 2017), respectively."></i>
                                            <a id="chart01-download" href="javascript:void(0);" target="_blank"
                                               title="download"><i
                                                    class="fa fa-download mr-1"></i></a>
                                        </h5>
                                        <div id="chart01" style="width: 10vw;height:700px; margin: auto"></div>
                                        <div id="chart01-msg"></div>
                                        <hr>
                                    </div>
                                    <div class="col-md-12">
                                        <h5 class="text-center">Taxonomically Distinctive Members between Different
                                            Groups
                                            <i class="fa fa-info-circle ml-1" data-toggle="tooltip"
                                               data-placement="right" data-container="body"
                                               data-html="true"
                                               title="The linear discriminant analysis (LDA) effect size (LEfSe) (Segata <i>et al</i>., 2011) was applied to identify taxonomically distinctive members for samples from different groups. Samples that don't contain any of the given taxonomic groups would be omitted in this plot."></i>
                                            <a id="chart02-download" href="javascript:void(0);" target="_blank"
                                               title="download"><i
                                                    class="fa fa-download mr-1"></i></a>
                                        </h5>
                                        <div id="chart02" style="width: 100%;height:400px;"></div>
                                        <div id="chart02-msg"></div>
                                        <hr>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="d-flex align-items-center justify-content-end px-2 mb-3">
                                            <ul class="nav results-tabs">

                                                <li class="nav-item">
                                                    <a class="nav-link pointer  active heatmap-method" value="bray"
                                                       data-toggle="tab"
                                                    >Bray-Curtis</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  heatmap-method" value="jaccard"
                                                       data-toggle="tab"
                                                    >Jaccard</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <h5 class="text-center">Heatmap of β-Diversity Distances
                                            <i class="fa fa-info-circle ml-1" data-toggle="tooltip"
                                               data-placement="right"
                                               data-container="body"
                                               data-html="true"
                                               title="Microbial communities cluster analysis by heatmap based on Bray-Curtis distance or Jaccard distance. Samples that don't contain any of the given taxonomic groups would be omitted in this plot."></i>
                                            <a id="chart03-download" href="javascript:void(0);" target="_blank"
                                               title="download"><i
                                                    class="fa fa-download mr-1"></i></a>
                                        </h5>
                                        <div id="chart03" style="width: 60vh;height:60vh;margin: auto"></div>
                                        <div id="chart03-msg"></div>
                                        <hr>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="d-flex align-items-center justify-content-end px-2 mb-3">
                                            <ul class="nav results-tabs">

                                                <li class="nav-item">
                                                    <a class="nav-link pointer  active pcoa-change-btn pcoa-method"
                                                       value="bray"
                                                       data-toggle="tab"
                                                    >Bray-Curtis</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link pointer  pcoa-change-btn pcoa-method"
                                                       value="jaccard"
                                                       data-toggle="tab"
                                                    >Jaccard</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <!--<div id="chart04" style="width: 100%;height:400px;"></div>
                                        <div id="chart04-msg"></div>-->
                                        <h5 class="text-center">Principal Coordinates Analysis of β-Diversity Distances
                                            <i class="fa fa-info-circle ml-1" data-toggle="tooltip"
                                               data-placement="right" data-container="body"
                                               data-html="true"
                                               title="Principal Coordinates Analysis (PCoA) based on Bray-Curtis distance or Jaccard distance showing the variations of microbial communities between different groups. Samples that don't contain any of the given taxonomic groups would be omitted in this plot."></i>
                                            <a id="chart04-download" target="_blank" href="javascript:void(0);"
                                               title="download"><i
                                                    class="fa fa-download mr-1"></i></a>
                                        </h5>
                                        <div class="d-flex justify-content-center h-50">

                                            <iframe id="chart04"
                                                    height="675px"
                                                    width="850px"
                                                    frameborder="0"></iframe>
                                            <div id="chart04-msg"></div>

                                        </div>

                                    </div>
                                    <div style="margin: auto">

                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
</div>
<th:block layout:fragment="custom-script">
    <script type="text/javascript" th:src="@{/assets/js/echarts/echarts.min.js}"></script>
    <!--<script type="text/javascript" th:src="@{/assets/js/echarts/bmap.js}"></script>-->
    <script type="text/javascript" th:src="@{/assets/js/pheatmap4js-0.3.1.js}"></script>

    <script>

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        let scatter_symbol = ['circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none']
        var groupColor = {
            GroupA: "#377EB8",
            GroupB: "#FF7F00",
            GroupC: "#E41A1C",
            GroupD: "#4DAF4A",
            GroupE: "#984EA3",
        }
        let color_theme = [
            "#0288D1", "#FF9800", "#F44336", "#26A69A", "#FFCA28", "#26C6DA",
            "#FFEE58", "#009688", "#8BC34A", "#AB47BC", "#CDDC39", "#FFC107",
            "#E91E63", "#9CCC65", "#795548", "#9C27B0", "#3F51B5", "#42A5F5",
            "#EF5350", "#00BCD4", "#66BB6A", "#FF5722", "#E6EE9C", "#3F51B5",
            "#FFEB3B", "#D4E157", "#673AB7", "#4CAF50", "#EC407A", "#9E9E9E"
        ]

        let taskId = $('#taskId').val()
        let chart1
        let chart2
        let chart3
        let chart4
        // 超过n位截取保留4位，不足n位补足0
        getFloat = function (number, n) {
            n = n ? parseInt(n) : 0;
            if (n <= 0) {
                return Math.round(number);
            }
            number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n); //四舍五入
            number = Number(number).toFixed(n); //补足位数
            return number;
        };


        $(function () {
            if ($('#taskId').val() !== "") {
                initChart01('A', 'P')
                initChart02('A', 'P')
                initChart03('A', 'P', 'bray')
                // initChart04('A', 'bray');
                initChart04I('A', 'P', 'bray');
                // initChart04()
            }
            $(".domain-change-btn").bind('click', function () {
                let domain = $(this).attr('value');
                let level = $("a[name='level'].active").attr("value");
                // console.log(taskId)
                if (taskId !== "") {
                    initChart01(domain, level)
                    initChart02(domain, level)
                    let pcoaMethod = $(".heatmap-method.active").attr("value");
                    initChart03(domain, level, pcoaMethod)
                    pcoaMethod = $(".pcoa-method.active").attr("value");
                    initChart04I(domain, level, pcoaMethod);
                }

            })
            $('.level-change-btn').bind('click', function () {
                let domain = $("a[name='domain'].active").attr("value");
                let level = $(this).attr('value');
                if (taskId !== "") {
                    initChart01(domain, level)
                    initChart02(domain, level)
                    let pcoaMethod = $(".heatmap-method.active").attr("value");
                    initChart03(domain, level, pcoaMethod)
                    pcoaMethod = $(".pcoa-method.active").attr("value");
                    initChart04I(domain, level, pcoaMethod);
                }
            })
            $(".heatmap-method").bind('click', function () {
                let domain = $("a[name='domain'].active").attr("value");
                let level = $("a[name='level'].active").attr("value");
                let method = $(this).attr('value')
                initChart03(domain, level, method);
            })
            $(".pcoa-change-btn").bind('click', function () {
                let domain = $("a[name='domain'].active").attr("value");
                let level = $("a[name='level'].active").attr("value");
                let method = $(this).attr('value')
                initChart04I(domain, level, method);
            })
        })

        function initChart01(domain, level) {
            // console.log(domain, level)
            if (!document.getElementById('chart01')) {
                return
            }
            $('#chart01-download').attr('href', `${_context_path}/diversity/downloadChart?chartNo=1&taskId=${taskId}&domain=${domain}&level=${level}`)

            drawChart()

            function drawChart() {

                $.ajax({
                    url: '/diversity/chartData',
                    data: {
                        'chartNo': 1,
                        'taskId': $("#taskId").val(),
                        'domain': domain != null ? domain : $("a[name='domain'].active").attr("value"),
                        'level': level != null ? level : $("a[name='level'].active").attr("value")
                    },
                    success: function (result) {
                        if (result.code !== 200) {
                            layer.msg(result.message)
                            return
                        }
                        if (result.code === 400 || result.code === 500 || Object.keys(result.data.data).length === 0) {
                            $("#chart01").hide()
                            $('#chart01-download').hide()
                            $("#chart01-msg").html('<div class="bg-light text-muted p-3 text-center">No Results</div>')
                            $("#chart01-msg").show()
                            return
                        }
                        $("#chart01").show()
                        $('#chart01-download').show()
                        $("#chart01-msg").hide()

                        let data = result.data.data
                        let groups = Object.keys(result.data.data);
                        let legend = result.data.legend;
                        let lengthArr = []
                        let legendColor = {}
                        legend.forEach((item, index) => {
                            legendColor[item] = color_theme[index]
                        })
                        let xAxisData = []
                        let titleData = []
                        for (let index in groups) {
                            let item = data[groups[index]]
                            for (let datumKey in item) {
                                let arr = []
                                item[datumKey].forEach(x => {
                                    arr.push({
                                        value: x[0],
                                        textStyle: {
                                            fontSize: 10
                                        }
                                    })
                                })
                                let obj = {
                                    type: 'category',
                                    name: groups[index],
                                    nameLocation: 'center',
                                    nameGap: -(Number($("#chart01").height()) * 0.72),
                                    nameTextStyle: {
                                        color: groupColor[groups[index]],
                                        fontSize: '16',
                                        fontWeight: 'bold',
                                    },
                                    data: arr,
                                    axisTick: {show: true, alignWithLabel: true},
                                    axisLine: {show: true},
                                    axisLabel: {
                                        rotate: 45,
                                        fontSize: 4
                                    },
                                    gridIndex: index
                                }
                                xAxisData.push(obj)
                                lengthArr.push(item[datumKey].length)
                                break
                            }
                        }
                        var j = eval(lengthArr.join('+'))
                        let left
                        if (j <= 10) {
                            $('#chart01').width('650px')
                            left = 8
                        } else if (j <= 50) {
                            $('#chart01').width('70%')
                            left = 8
                        } else {
                            $('#chart01').width('100%')
                            left = 5
                        }
                        if (chart1 != null) {
                            chart1.dispose()
                        }
                        chart1 = echarts.init(document.getElementById('chart01'))

                        let percentage = countPercentage(lengthArr);
                        let gridData = []
                        let yAxisData = []
                        let publicStall = left / percentage.length
                        let groupInterval = 0.5
                        for (let i = 0; i < percentage.length; i++) {
                            let per = percentage[i] - publicStall - groupInterval;
                            let right = 100.2 - left - per
                            let obj = {
                                top: '6%',
                                bottom: '25%',
                                right: right + '%',
                                left: left + '%',
                            }
                            gridData.push(obj)
                            yAxisData.push({
                                max: 100,
                                gridIndex: i,
                                name: 'Abundance (%)',
                                nameLocation: 'center',
                                nameRotate: 90,
                                nameGap: 30,
                                nameTextStyle: {
                                    fontSize: 14,
                                    fontWeight: 'normal'
                                },
                                type: 'value',
                                splitNumber: 10,
                                splitLine: {show: false},
                                axisTick: {show: true},
                                axisLine: {show: true},
                                show: i === 0,
                            })
                            // let titleObj = {
                            //     text: groups[i],
                            //     textStyle: {
                            //         color: groupColor[groups[i]],
                            //         fontSize: '16'
                            //     },
                            //     left: ((left + percentage[i] / 2) - (groups[i].length) / 2) + '%',
                            //     top: '2%'
                            // }
                            // titleData.push(titleObj)
                            left = left + per + groupInterval
                        }
                        let seriesData = []
                        for (let index in groups) {
                            let value = groups[index];
                            let item = data[value]
                            let i = 0
                            for (let key in item) {

                                let data = item[key].map(it => {
                                    return it[3]
                                });
                                let obj = {
                                    name: key,
                                    type: 'bar',
                                    stack: value,
                                    barWidth: (data.length <= 10 && groups.length === 2) ? '95%' : '103%',
                                    data: data,
                                    itemStyle: {
                                        color: legendColor[key],
                                        // color: color_theme[i % color_theme.length],
                                    },
                                    xAxisIndex: index,
                                    yAxisIndex: index
                                }
                                i++
                                seriesData.push(obj)
                            }

                        }
                        // 添加标题
                        // titleData.push({
                        //     text: "group",
                        //     textStyle: {
                        //         fontSize: '14',
                        //         fontWeight: 'normal'
                        //     },
                        //     left: '48.5%',
                        //     top: '80%'
                        // })
                        let legendWidth = 70
                        let legendFontSize = 12
                        if (j <= 10 && legend.length >= 23) {
                            legendWidth = 65
                            legendFontSize = 10
                        } else {
                            legendWidth = 70
                            legendFontSize = 12
                        }
                        let option = {
                            grid: gridData,
                            legend: {
                                top: '83%',
                                left: 'center',
                                right: 'center',
                                data: legend,
                                // type: 'scroll',
                                orient: 'horizontal',
                                pageButtonItemGap: 10,
                                formatter: function (name) {
                                    name = name.length > 10 ? name.substr(0, 7) + '...' : name
                                    return [`{a|${name}}`].join('\n');
                                },
                                textStyle: {
                                    rich: {
                                        a: {
                                            width: legendWidth, // 每个图例的宽度，具体根据字数调整
                                            fontSize: legendFontSize,
                                            lineHeight: 6
                                        }
                                    }
                                },
                                align: 'auto',
                                tooltip: {
                                    show: true
                                }
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return `${params.marker}&nbsp;${params.seriesName}<br/>
                            Run ID:&nbsp;${params.name}<br/>
                            Abundance:&nbsp;${params.data}<br/>`;
                                }
                            },
                            title: titleData,
                            xAxis: xAxisData,
                            yAxis: yAxisData,
                            series: seriesData
                        };
                        chart1.setOption(option)
                    }
                })
            }
        }

        function initChart02(domain, level) {
            if (!document.getElementById('chart02')) {
                return
            }
            $('#chart02-download').attr('href', `${_context_path}/diversity/downloadChart?chartNo=2&taskId=${taskId}&domain=${domain}&level=${level}`)

            drawChart()

            function drawChart() {
                $.ajax({
                    url: '/diversity/chartData',
                    data: {
                        'chartNo': 2,
                        'taskId': taskId,
                        'domain': domain,
                        'level': level
                    },
                    success: function (result) {

                        if (result.code !== 200 || Object.keys(result.data.data).length === 0) {
                            $("#chart02").hide()
                            $('#chart02-download').hide()
                            $("#chart02-msg").html('<div class="bg-light text-muted p-3 text-center">No significant differences in abundance across groups</div>')
                            $("#chart02-msg").show()
                            return
                        }
                        let data = result.data.data
                        let groups = result.data.groups
                        $("#chart02").show()
                        $('#chart02-download').show()
                        $("#chart02-msg").hide()
                        // console.log(result)
                        // console.log('length', Object.keys(result.data).length)
                        // console.log('chart2', data, groups)
                        // let legendData = Object.keys(data).map(x => {
                        //     return x
                        // })
                        let legendData = []
                        groups.forEach(x => {
                            if (Object.keys(data).indexOf(x) !== -1) {
                                legendData.push(x)
                            }
                        })
                        // console.log('chart2data', data)
                        // console.log('chart2data', groups)
                        // console.log('chart2data', legendData)

                        let option = {
                            grid: {
                                left: "23%",
                                right: "23%"
                            },

                            title: {
                                text: '',
                                textStyle: {
                                    color: '#666',
                                    fontSize: '16'
                                },
                                subtext: 'A barplot of the LDA values distribution, LDA ≥ 2, P-value ≤ 0.05',

                                left: 'center'
                            },
                            legend: {
                                right: '15%',
                                top: 'middle',
                                data: legendData,
                                orient: 'vertical',
                                type: 'scroll',
                                width: 110,
                                textStyle: {
                                    fontSize: 14
                                }
                            },
                            xAxis: {
                                type: 'value',
                                name: 'LDA Score（log 10）',
                                nameLocation: 'center',
                                textStyle: {
                                    fontSize: 14
                                },
                                nameGap: 25,
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        type: 'dashed'
                                    }
                                },
                            },
                            yAxis: {
                                type: 'category',

                                data: (function () {
                                    let list = [];
                                    legendData.forEach((key) => {
                                        // console.log(key)
                                        let value = data[key];
                                        value.forEach((x) => {
                                            list.push(x[0]);
                                        });
                                    });
                                    // console.log('yAxix', list);
                                    return list;
                                })(),

                                inverse: true,
                                show: false
                            },
                            tooltip: {
                                formatter: function (params) {
                                    let node = params;
                                    var value = Math.abs(node.value[0]);
                                    //分类名称<br />左边列字符名称<br />p.Adjust: 原始的p.Adjust值
                                    return `${node.marker}${node.seriesName}<br/>
                            ${node.value[1]}&nbsp&nbsp${getFloat(value, 4)}`;
                                }
                            },
                            series: (function () {
                                let list = [];
                                Object.keys(data).forEach((key) => {
                                    let name = key;
                                    let val = data[key].map((x) => {
                                        // console.log(legendData.indexOf(key));
                                        return [
                                            ((legendData.length === 2 && legendData.indexOf(key) === 1) ? -1 : 1) * x[1],
                                            x[0],
                                        ];
                                    });
                                    let obj = {
                                        name: name,
                                        type: 'bar',
                                        stack: 'Total',
                                        itemStyle: {
                                            color: groupColor[key],
                                        },
                                        label: {
                                            show: true,
                                            position: ((legendData.length === 2 && legendData.indexOf(key) === 1) ? 'right' : 'left'),
                                            formatter: '{b}'
                                        },

                                        data: val
                                    };
                                    list.push(obj);
                                });
                                return list;
                            })()
                        };
                        if (chart2 != null) {
                            chart2.dispose()
                        }
                        chart2 = echarts.init(document.getElementById('chart02'))

                        chart2.setOption(option)
                    }
                })
            }
        }


        function initChart03(domain, level, method) {
            if (!document.getElementById('chart03')) {
                return
            }
            $('#chart03-download').attr('href', `${_context_path}/diversity/downloadChart?chartNo=3&taskId=${taskId}&domain=${domain}&level=${level}&betaMethod=${method}`)

            drawChart()

            function drawChart() {

                $.ajax({
                    url: '/diversity/chartData',
                    data: {
                        'chartNo': 3,
                        'taskId': taskId,
                        'domain': domain,
                        'level': level,
                        'betaMethod': method,
                    },
                    success: function (result) {
                        // console.log(result)
                        // console.log('length', Object.keys(result.data).length)

                        if (result.code !== 200 || Object.keys(result.data.data).length === 0) {
                            $("#chart03").hide()
                            $('#chart03-download').hide()
                            $("#chart03-msg").html('<div class="bg-light text-muted p-3 text-center">No Results</div>')
                            $("#chart03-msg").show()
                            return
                        }
                        $("#chart03").show()
                        $('#chart03-download').show()
                        $("#chart03-msg").hide()
                        let data = result.data.data;
                        let groupInfo = result.data.groupInfo

                        let groups = []
                        Object.keys(groupInfo).forEach(key => {
                            if (groups.indexOf(groupInfo[key]) === -1) {
                                groups.push(groupInfo[key])
                            }
                        })

                        let cols = [];

                        for (let i = 1; i < data[0].length; i++) {
                            cols.push(groupInfo[data[0][i]])
                        }

                        let annotation_colorsGroup = {}
                        groups.forEach(x => {
                            annotation_colorsGroup[x] = groupColor[x]
                        })

                        let fontsizeNum = 8
                        if (data.length >= 50) {
                            $('#chart03').width('900px')
                            $('#chart03').height('900px')
                            fontsizeNum = 8
                        } else if (data.length >= 10) {
                            $('#chart03').width('800px')
                            $('#chart03').height('800px')
                            fontsizeNum = 10
                        } else {
                            $('#chart03').width('700px')
                            $('#chart03').height('700px')
                            fontsizeNum = 12
                        }
                        // console.log('chart3', result.data)
                        // console.log('fontsizeNum', fontsizeNum)


                        let option = {
                            server_host: 'https://www.biosino.org/vipmap/BioCharts/api',
                            mat: data,
                            // color: ["#4575B4", "#FFFFBF", "#D73027"],
                            legend: true,
                            show_rownames: true,
                            show_colnames: true,
                            // main: '层次聚类热力图',
                            fontsize: Number(fontsizeNum),
                            // fontsize_col: 8,
                            // fontsize_row: 8,
                            angle_col: 90,
                            display_numbers: false,
                            number_format: '%.2f',
                            number_color: '#363636',
                            fontsize_number: 8,
                            cluster_rows: true,
                            cluster_cols: true,

                            annotation_row: {
                                group: cols,
                            },
                            annotation_col: {
                                group: cols,
                            },
                            annotation_colors: {
                                group: annotation_colorsGroup,
                            },
                            annotation_legend: true,
                            annotation_names_row: false,
                            annotation_names_col: false,
                        }

                        if (chart3 != null) {
                            chart3.dispose()
                        }
                        pheatmap4js.render('chart03', option)
                        chart3 = echarts.getInstanceByDom(document.getElementById('chart03'))
                    }
                })
            }
        }


        function initChart04(domain, method) {
            if (!document.getElementById('chart04')) {
                return
            }
            drawChart()

            function drawChart() {

                $.ajax({
                    url: '/diversity/chartData',
                    data: {
                        'chartNo': 4,
                        'taskId': $("#taskId").val(),
                        'domain': domain,
                        'betaMethod': method
                    },
                    success: function (result) {
                        if (result.code !== 200) {
                            layer.msg(result.message)
                            return
                        }

                        if (result.code === 400 || result.code === 500 || Object.keys(result.data).length === 0) {
                            $("#chart04").hide()
                            $("#chart04-msg").html('<div class="bg-light text-muted p-3 text-center">No Results</div>')
                            $("#chart04-msg").show()
                            return
                        }
                        $("#chart04").show()
                        $("#chart04-msg").hide()
                        chart4 = echarts.init(document.getElementById('chart04'))
                        chart4.clear()
                        let data = result.data
                        let groups = Object.keys(data);

                        let option = {
                            title: {
                                text: 'Pcoa plot by Group',
                                textStyle: {
                                    color: '#666',
                                    fontSize: '16'
                                },
                                left: 'center',
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return `${params.marker}&nbsp;${params.seriesName}<br/>
                            PCoA1:&nbsp;${params.value[0]}<br/>
                            PCoA2:&nbsp;${params.value[1]}<br/>`;
                                }
                            },
                            grid: {
                                top: '10%',
                                left: '15%',
                                right: '15%',
                                bottom: '50'
                            },
                            legend: {
                                right: '2%',
                                top: 'center',
                                type: 'scroll',
                                orient: 'vertical',
                                formatter: function (name) {
                                    return name.length > 10 ? name.substr(0, 10) + "..." : name;
                                }
                            },
                            xAxis: {
                                name: 'PCoA1',
                                nameLocation: 'center',
                                nameGap: 25,
                                nameTextStyle: {
                                    color: '#666',
                                },
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'
                                    }
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                            },
                            yAxis: {
                                name: 'PCoA2',
                                nameLocation: 'middle',
                                nameGap: 65,
                                nameTextStyle: {
                                    color: '#666',
                                },
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'
                                    }
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                scale: true
                            },
                            series: (function () {
                                let list = []
                                let i = 0
                                Object.keys(data).forEach(key => {
                                    let name = key
                                    let val = data[key].map(x => {
                                        return [x[1], x[2], x[0]]
                                    });
                                    let obj = {
                                        name: key,
                                        data: val,
                                        type: 'scatter',
                                        symbol: scatter_symbol[i]
                                    }
                                    list.push(obj)
                                    i++
                                })
                                return list;
                            })()
                        }

                        chart4.setOption(option)
                    }
                })
            }
        }

        function initChart04I(domain, level, method) {
            $('#chart04-download').attr('href', `${_context_path}/diversity/downloadChart?chartNo=4&taskId=${taskId}&domain=${domain}&level=${level}&betaMethod=${method}`)

            $.ajax({
                url: '/diversity/chartData',
                data: {
                    'chartNo': 4,
                    'taskId': $("#taskId").val(),
                    'domain': domain,
                    'level': level,
                    'betaMethod': method
                },
                success: function (result) {
                    if (result.code !== 200) {
                        layer.msg(result.message)
                        return
                    }

                    if (result.code === 400 || result.code === 500 || result.data === false) {
                        $("#chart04").hide()
                        $("#chart04-download").hide()
                        $("#chart04-msg").html('<div class="bg-light text-muted p-3 text-center">No Results</div>')
                        $("#chart04-msg").show()
                        return
                    }
                    $("#chart04").show()
                    $("#chart04-download").show()
                    $("#chart04-msg").hide()
                    $("#chart04").attr("src", `${_context_path}/diversity/result/${taskId}/PCoA?domain=${domain}&level=${level}&method=${method}`)
                }
            })
            // // console.log(domain, method)
            // $("#pcoaIframe").attr("src", `${_context_path}/diversity/result/${taskId}/PCoA?domain=${domain}&method=${method}`)
        }

        window.onresize = function () {
            if (chart1 != null) {
                chart1.resize();
            }
            if (chart2 != null) {
                chart2.resize();
            }
            if (chart3 != null) {
                chart3.resize();
            }
            // chart4.resize();
        };

    </script>

</th:block>
</html>
