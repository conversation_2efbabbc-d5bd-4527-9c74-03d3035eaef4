# 指定清华 CRAN 镜像
cran_repo <- "https://mirror.lzu.edu.cn/CRAN/"

# 定义函数
ensure_package <- function(pkg, repo = cran_repo) {
  if (!suppressMessages(require(pkg, character.only = TRUE, quietly = TRUE))) {
    install.packages(pkg, repos = repo)
    suppressMessages(library(pkg, character.only = TRUE))
  } else {
    suppressMessages(library(pkg, character.only = TRUE))
  }
}

# 使用示例
package_list <- c("GetoptLong", "ggplot2", "tidyverse","plotly","htmlwidgets")
for (p in package_list) {
  ensure_package(p)
}

rm(list = ls(all.names = TRUE))

# ✅ 添加新参数 SelectDomain 和 SelectTaxonomy
GetoptLong(
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Group_color=s", "group table, e.g., group_color.txt with two coloumn: Group and Color",
  "Input=s", "input Dir for PCoA Table, e.g., PCoA_Table",
  "Output=s", "output Dir for PCoA figure, e.g., PCoA_Figure",
  "SelectDomain=s", "choose one Domain only, e.g., B",
  "SelectTaxonomy=s", "choose one taxonomy level, e.g., S",
  "verbose!", "print messages"
)

##########################      Data input      ##########################
group = read.csv(Group, sep = '\t', header = TRUE, row.names = 1, check.names = F)
levels_group = sort(unique(group$group))
group$group = factor(group$group, levels = levels_group)
group$sample_name <- rownames(group)

Group_color_data = read.csv(Group_color, sep = '\t', header = TRUE, col.names = c('Group', 'Color'),
                            comment.char = "", stringsAsFactors = FALSE, check.names = F) %>%
  {.[.$Group %in% unique(group$group), ]}
rownames(Group_color_data) = Group_color_data$Group

if (!dir.exists(Output)) {
  dir.create(Output)
}

##########################   只运行指定 Domain 和 Level   ##########################
domain <- SelectDomain
Beta_level <- SelectTaxonomy

for (beta in c('bray', 'jaccard')) {
  message("Domain: ", domain)
  message("Beta_level: ", Beta_level)
  message("Method: ", beta)

  label_file_name = file.path(Input, paste0("PCoA.label.", domain, ".", Beta_level, ".", beta, ".csv"))
  plot_file_name  = file.path(Input, paste0("PCoA.", domain, ".", Beta_level, ".", beta, ".csv"))

  if (file.exists(label_file_name) & file.exists(plot_file_name)) {
    label = read.csv(label_file_name, sep = ',', header = TRUE, check.names = F)
    file_pcoa = read.csv(plot_file_name, sep = ',', header = TRUE, check.names = F)

    levels_group = group %>%
      {.[.$sample_name %in% unique(file_pcoa$sample_name), ]} %>%
      {sort(unique(.$group))}

    Group_color_each_map = Group_color_data %>%
      {.[.$Group %in% levels_group, ]} %>%
      {.[match(levels_group, rownames(.)), ]} %>%
      {mutate(., group = Group)}

    plot_pcoa = file_pcoa %>%
      mutate(
        Group = factor(group, levels = levels_group),
        PCo1 = as.numeric(sprintf("%0.4f", Axis.1)),
        PCo2 = as.numeric(sprintf("%0.4f", Axis.2))
      )

    # Static PDF
    p3.8 = ggplot(plot_pcoa) +
      stat_ellipse(aes(x = PCo1, y = PCo2, fill = Group), level = 0.95, alpha = 0.15, geom = "polygon") +
      geom_point(aes(x = PCo1, y = PCo2, color = Group, text = paste("Run ID: ", sample_name)), alpha = 0.7, size = 5) +
      scale_color_manual(values = Group_color_each_map$Color) +
      scale_fill_manual(values = Group_color_each_map$Color) +
      labs(x = paste0("PCo1 (", label$x_label, "%)"),
           y = paste0("PCo2 (", label$y_label, "%)")) +
      theme_bw() +
      theme(panel.grid = element_blank())

    ggsave(
      filename = paste0("PCoA.", domain, ".", Beta_level, ".", beta, ".pdf"),
      plot = p3.8,
      path = Output,
      width = 10,
      height = 8
    )

    # Interactive HTML
    p3.7 = ggplot(plot_pcoa, aes(x = PCo1, y = PCo2, fill = Group)) +
      stat_ellipse(level = 0.95, alpha = 0.15, geom = "polygon") +
      geom_point(aes(text = paste("Run ID: ", sample_name)), alpha = 0.7, size = 5, color = NA) +
      scale_fill_manual(values = Group_color_each_map$Color) +
      labs(x = paste0("PCo1 (", label$x_label, "%)"),
           y = paste0("PCo2 (", label$y_label, "%)")) +
      theme_bw() +
      theme(panel.grid = element_blank())

    pp <- plotly_build(p3.7)
    saveWidget(
      pp,
      file = file.path(Output, paste0("PCoA.", domain, ".", Beta_level, ".", beta, ".html")),
      libdir = "./HTMLdependencies"
    )
  } else {
    message("File missing: ", label_file_name, " or ", plot_file_name)
  }
}

