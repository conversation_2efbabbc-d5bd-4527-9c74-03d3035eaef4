#! /usr/bin/perl
if(@ARGV < 6){
    print "step6-Kraken_taxonomy2Community.pl kraken_merge_file sample_list outdir Domain level_input metadata\n";
    exit;
}
my $kraken = shift;
my $sample = shift;
my $outdir = shift;
my $domain = shift;
my $level_input = shift;  # 新增：指定的level
my $metadata = shift;

# 读取样本列表
my %rename;
my @raw_names;
my @samples;
open(SAMPLE, "<$sample") or die;
<SAMPLE>; 
while (<SAMPLE>) {
    chomp $_;
    my @line = split /\t/, $_;
    $rename{$line[0]} = $line[0];
    $samples[@samples] = $line[0];
}
close(SAMPLE);

# 读取分组信息
my %groups;
open(GROUP, "<$metadata") or die;
my $group_header = <GROUP>;
chomp $group_header;
while (<GROUP>) {
    chomp $_;
    my @line = split /\t/, $_;
    $groups{$line[0]} = $line[1];
}
close(GROUP);

`mkdir -p  $outdir`;



open(INCLUDE, ">$outdir/include.xls") or die;
open(EXCLUDE, ">$outdir/exclude.xls") or die;
open(GROUP2, ">$outdir/group.txt") or die;
print GROUP2 "$group_header\n";

my %percent;
my %percent2;
my %level;
my %includes;
my @sample_include;
open(KRAKEN, "<$kraken") or die;
my $header = <KRAKEN>;
if ($header =~ /^#/) {
    $header = <KRAKEN>;
}
chomp $header;
my @headers = split /\t/, $header;
while (<KRAKEN>) {
    chomp $_;
    my @line = split /\t/, $_;
    if ($line[2] ne $domain) {
        next;
    }
    if ($line[1] eq "D") {
        for (my $i = 3; $i < @line; $i++) {
            if ($line[$i] > 0) {
                print INCLUDE "$headers[$i]\t$headers[$i]\n";
                $raw_names[@raw_names] = $headers[$i];
                $sample_include[@sample_include] = $headers[$i];
                $includes{$headers[$i]} = 1;
            } else {
                print EXCLUDE "$headers[$i]\t$headers[$i]\n";
            }
        }
    }
    $line[0] =~ s/ /_/g;
    my $tmp = $line[1] . "||" . $line[0];
    $level{$tmp} = $line[1];
    for (my $i = 2; $i < @line; $i++) {
        $percent{$headers[$i]}{$tmp} = $line[$i];
        $percent2{$headers[$i]}{$tmp} = $line[$i] / 100;
    }   
}
close(KRAKEN);
close(INCLUDE);
close(EXCLUDE);

# 读取并输出分组信息
open(GROUP, "<$metadata") or die;
while (<GROUP>) {
    chomp $_;
    my @line = split /\t/, $_;
    if ($includes{$line[0]} == 1) {
        print GROUP2 "$line[0]\t$groups{$line[0]}\n";    
    }
}
close(GROUP);
close(GROUP2);

# 创建输出文件，文件名根据level输入来决定
if ($level_input eq "P") {
    open(PHYLUM, ">$outdir/phylum.xls") or die;
    open(PHYLUM2, ">$outdir/phylum.percents.xls") or die;
    print PHYLUM "Taxon\t" . join("\t", @sample_include) . "\n";
    print PHYLUM2 "# Constructed from biom file\nTaxon\t" . join("\t", @sample_include) . "\n";
    foreach my $key (sort {$a cmp $b} keys %level) {
        if ($level{$key} eq "P") {
            $key =~ /(\S)\|\|(\S+)/;
            print PHYLUM "$2\t";
            print PHYLUM2 "$2\t";
            for (my $j = 0; $j < @raw_names - 1; $j++) {
                print PHYLUM "$percent{$raw_names[$j]}{$key}\t";
                print PHYLUM2 "$percent2{$raw_names[$j]}{$key}\t"; 
            } 
            print PHYLUM "$percent{$raw_names[@raw_names-1]}{$key}\n";
            print PHYLUM2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
        }
    }
    close(PHYLUM);
    close(PHYLUM2);
} elsif ($level_input eq "C") {
    # 类似地，你可以在这里处理class层级
    open(CLASS, ">$outdir/class.xls") or die;
    open(CLASS2, ">$outdir/class.percents.xls") or die;
    print CLASS "Taxon\t" . join("\t", @sample_include) . "\n";
    print CLASS2 "# Constructed from biom file\nTaxon\t" . join("\t", @sample_include) . "\n";
    foreach my $key (sort {$a cmp $b} keys %level) {
        if ($level{$key} eq "C") {
            $key =~ /(\S)\|\|(\S+)/;
            print CLASS "$2\t";
            print CLASS2 "$2\t";
            for (my $j = 0; $j < @raw_names - 1; $j++) {
                print CLASS "$percent{$raw_names[$j]}{$key}\t";
                print CLASS2 "$percent2{$raw_names[$j]}{$key}\t";
            }
            print CLASS "$percent{$raw_names[@raw_names-1]}{$key}\n";
            print CLASS2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
        }
    }
    close(CLASS);
    close(CLASS2);
} elsif ($level_input eq "O") {
    # 处理order层级
    open(ORDER, ">$outdir/order.xls") or die;
    open(ORDER2, ">$outdir/order.percents.xls") or die;
    print ORDER "Taxon\t" . join("\t", @sample_include) . "\n";
    print ORDER2 "# Constructed from biom file\nTaxon\t" . join("\t", @sample_include) . "\n";
    foreach my $key (sort {$a cmp $b} keys %level) {
        if ($level{$key} eq "O") {
            $key =~ /(\S)\|\|(\S+)/;
            print ORDER "$2\t";
            print ORDER2 "$2\t";
            for (my $j = 0; $j < @raw_names - 1; $j++) {
                print ORDER "$percent{$raw_names[$j]}{$key}\t";
                print ORDER2 "$percent2{$raw_names[$j]}{$key}\t";
            }
            print ORDER "$percent{$raw_names[@raw_names-1]}{$key}\n";
            print ORDER2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
        }
    }
    close(ORDER);
    close(ORDER2);
} elsif ($level_input eq "F") {
    # 处理family层级
    open(FAMILY, ">$outdir/family.xls") or die;
    open(FAMILY2, ">$outdir//family.percents.xls") or die;
    print FAMILY "Taxon\t" . join("\t", @sample_include) . "\n";
    print FAMILY2 "# Constructed from biom file\nTaxon\t" . join("\t", @sample_include) . "\n";
    foreach my $key (sort {$a cmp $b} keys %level) {
        if ($level{$key} eq "F") {
            $key =~ /(\S)\|\|(\S+)/;
            print FAMILY "$2\t";
            print FAMILY2 "$2\t";
            for (my $j = 0; $j < @raw_names - 1; $j++) {
                print FAMILY "$percent{$raw_names[$j]}{$key}\t";
                print FAMILY2 "$percent2{$raw_names[$j]}{$key}\t";
            }
            print FAMILY "$percent{$raw_names[@raw_names-1]}{$key}\n";
            print FAMILY2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
        }
    }
    close(FAMILY);
    close(FAMILY2);
} elsif ($level_input eq "G") {
    # 处理genus层级
    open(GENUS, ">$outdir/genus.xls") or die;
    open(GENUS2, ">$outdir/genus.percents.xls") or die;
    print GENUS "Taxon\t" . join("\t", @sample_include) . "\n";
    print GENUS2 "# Constructed from biom file\nTaxon\t" . join("\t", @sample_include) . "\n";
    foreach my $key (sort {$a cmp $b} keys %level) {
        if ($level{$key} eq "G") {
            $key =~ /(\S)\|\|(\S+)/;
            print GENUS "$2\t";
            print GENUS2 "$2\t";
            for (my $j = 0; $j < @raw_names - 1; $j++) {
                print GENUS "$percent{$raw_names[$j]}{$key}\t";
                print GENUS2 "$percent2{$raw_names[$j]}{$key}\t";
            }
            print GENUS "$percent{$raw_names[@raw_names-1]}{$key}\n";
            print GENUS2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
        }
    }
    close(GENUS);
    close(GENUS2);
} elsif ($level_input eq "S") {
    # 处理species层级
    open(SPECIES, ">$outdir/species.xls") or die;
    open(SPECIES2, ">$outdir/species.percents.xls") or die;
    print SPECIES "Taxon\t" . join("\t", @sample_include) . "\n";
    print SPECIES2 "# Constructed from biom file\nTaxon\t" . join("\t", @sample_include) . "\n";
    foreach my $key (sort {$a cmp $b} keys %level) {
        if ($level{$key} eq "S") {
            $key =~ /(\S)\|\|(\S+)/;
            print SPECIES "$2\t";
            print SPECIES2 "$2\t";
            for (my $j = 0; $j < @raw_names - 1; $j++) {
                print SPECIES "$percent{$raw_names[$j]}{$key}\t";
                print SPECIES2 "$percent2{$raw_names[$j]}{$key}\t";
            }
            print SPECIES "$percent{$raw_names[@raw_names-1]}{$key}\n";
            print SPECIES2 "$percent2{$raw_names[@raw_names-1]}{$key}\n";
        }
    }
    close(SPECIES);
    close(SPECIES2);
}
