import request from '@/utils/request';

const baseURL = '/genomic';

// 创建biogeography任务
export function createBiogeography(data) {
  return request({
    url: `${baseURL}/biogeography`,
    method: 'post',
    data: data,
  });
}

// 获取KO列表
export function getKoList(data) {
  return request({
    url: `${baseURL}/getKoList`,
    method: 'post',
    data: data,
  });
}

// 获取表格结果
export function getTableResult(data) {
  return request({
    url: `${baseURL}/getTableResult`,
    method: 'post',
    data: data,
  });
}

// 获取Gene Name下拉框数据
export function getPathKoToNameList() {
  return request({
    url: `${baseURL}/getPathKoToNameList`,
    method: 'post',
  });
}

// 获取Pathway-KO下拉框数据
export function getPathwayName() {
  return request({
    url: `${baseURL}/getPathwayName`,
    method: 'post',
  });
}

// 获取地图数据
export function getResultMapData(data) {
  return request({
    url: `${baseURL}/getResultMapData`,
    method: 'post',
    data: data,
  });
}

// 创建Function Analysis任务
export function createFunctionAnalysis(data) {
  return request({
    url: `${baseURL}/functionAnalysis`,
    method: 'post',
    data: data,
  });
}
