package org.biosino.lf.mash.mashweb.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/21
 */
@Data
public class SamplesBioGeographyVO {

    @Alias("RUN_ID")
    private String runId;

    private String group;

    @Alias("Value")
    private Double value;

    private String bioProjectId;

    private String latitude;

    private String longitude;

    private String hydrosphereType;

    private String geoLocName;

    private String waterBodyTypeByClassification;

    private String waterBodyTypeByGeographic;

    private String waterBodyType;

    private String depth;

    private String temperature;

    private String salinity;

    private String ph;

    private String criticalZone;

    private String samplingSubstrate;

    private String country;

    private String waterBodyName;
}
